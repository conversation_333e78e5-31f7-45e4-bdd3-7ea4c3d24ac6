<template>
  <ClientOnly>
    <div class="upload-container">
      <div
        class="dropzone"
        :class="{ 'drag-over': dragOver }"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          @change="handleFileSelection"
          :accept="acceptTypes"
        />

        <template v-if="files.length == 0">
          <svg
            width="504"
            height="283"
            viewBox="0 0 504 283"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask
              id="mask0_33_10885"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="12"
              y="-221"
              width="480"
              height="480"
            >
              <rect
                width="480"
                height="480"
                transform="translate(12 -221)"
                fill="url(#paint0_radial_33_10885)"
              />
            </mask>
            <g mask="url(#mask0_33_10885)">
              <g clip-path="url(#clip0_33_10885)">
                <g clip-path="url(#clip1_33_10885)">
                  <line
                    x1="12.5"
                    y1="-221"
                    x2="12.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="44.5"
                    y1="-221"
                    x2="44.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="76.5"
                    y1="-221"
                    x2="76.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="108.5"
                    y1="-221"
                    x2="108.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="140.5"
                    y1="-221"
                    x2="140.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="172.5"
                    y1="-221"
                    x2="172.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="204.5"
                    y1="-221"
                    x2="204.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="236.5"
                    y1="-221"
                    x2="236.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="268.5"
                    y1="-221"
                    x2="268.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="300.5"
                    y1="-221"
                    x2="300.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="332.5"
                    y1="-221"
                    x2="332.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="364.5"
                    y1="-221"
                    x2="364.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="396.5"
                    y1="-221"
                    x2="396.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="428.5"
                    y1="-221"
                    x2="428.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="460.5"
                    y1="-221"
                    x2="460.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                </g>
                <rect
                  x="12.5"
                  y="-220.5"
                  width="479"
                  height="479"
                  stroke="#EAECF0"
                />
                <g clip-path="url(#clip2_33_10885)">
                  <line x1="12" y1="2.5" x2="492" y2="2.5" stroke="#EAECF0" />
                  <line x1="12" y1="34.5" x2="492" y2="34.5" stroke="#EAECF0" />
                  <line x1="12" y1="66.5" x2="492" y2="66.5" stroke="#EAECF0" />
                  <line x1="12" y1="98.5" x2="492" y2="98.5" stroke="#EAECF0" />
                  <line
                    x1="12"
                    y1="130.5"
                    x2="492"
                    y2="130.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="162.5"
                    x2="492"
                    y2="162.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="194.5"
                    x2="492"
                    y2="194.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="226.5"
                    x2="492"
                    y2="226.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="258.5"
                    x2="492"
                    y2="258.5"
                    stroke="#EAECF0"
                  />
                </g>
                <rect
                  x="12.5"
                  y="-220.5"
                  width="479"
                  height="479"
                  stroke="#EAECF0"
                />
              </g>
            </g>
            <defs>
              <radialGradient
                id="paint0_radial_33_10885"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(240 240) rotate(90) scale(240 240)"
              >
                <stop />
                <stop offset="1" stop-opacity="0" />
              </radialGradient>
              <clipPath id="clip0_33_10885">
                <rect
                  width="480"
                  height="480"
                  fill="white"
                  transform="translate(12 -221)"
                />
              </clipPath>
              <clipPath id="clip1_33_10885">
                <rect x="12" y="-221" width="480" height="480" fill="white" />
              </clipPath>
              <clipPath id="clip2_33_10885">
                <rect x="12" y="-221" width="480" height="480" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <svg
            width="152"
            height="120"
            viewBox="0 0 152 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="76" cy="52" r="52" fill="#EAECF0" />
            <g filter="url(#filter0_dd_33_10927)">
              <path
                d="M77.6 16C66.8273 16 57.2978 21.3233 51.4987 29.4829C49.605 29.0363 47.6301 28.8 45.6 28.8C31.4615 28.8 20 40.2615 20 54.4C20 68.5385 31.4615 80 45.6 80L109.6 80C121.971 80 132 69.9712 132 57.6C132 45.2288 121.971 35.2 109.6 35.2C108.721 35.2 107.854 35.2506 107.002 35.349C102.098 23.9677 90.7797 16 77.6 16Z"
                fill="#F9FAFB"
              />
              <ellipse
                cx="45.6"
                cy="54.3998"
                rx="25.6"
                ry="25.6"
                fill="url(#paint0_linear_33_10927)"
              />
              <circle
                cx="77.5996"
                cy="48"
                r="32"
                fill="url(#paint1_linear_33_10927)"
              />
              <ellipse
                cx="109.6"
                cy="57.6002"
                rx="22.4"
                ry="22.4"
                fill="url(#paint2_linear_33_10927)"
              />
            </g>
            <circle cx="21" cy="19" r="5" fill="#F2F4F7" />
            <circle cx="18" cy="109" r="7" fill="#F2F4F7" />
            <circle cx="145" cy="35" r="7" fill="#F2F4F7" />
            <circle cx="134" cy="8" r="4" fill="#F2F4F7" />
            <path
              d="M52 86C52 72.7452 62.7452 62 76 62C89.2548 62 100 72.7452 100 86C100 99.2548 89.2548 110 76 110C62.7452 110 52 99.2548 52 86Z"
              fill="#344054"
              fill-opacity="0.4"
            />
            <path
              d="M84.3904 92.3905C85.3658 91.8587 86.1363 91.0174 86.5803 89.9991C87.0244 88.9808 87.1167 87.8437 86.8427 86.7672C86.5686 85.6906 85.9439 84.736 85.0671 84.0539C84.1903 83.3719 83.1113 83.0012 82.0004 83.0005H80.7404C80.4378 81.8297 79.8736 80.7428 79.0904 79.8215C78.3072 78.9001 77.3253 78.1683 76.2185 77.6811C75.1118 77.1938 73.909 76.9638 72.7006 77.0084C71.4921 77.0529 70.3095 77.3708 69.2416 77.9381C68.1737 78.5055 67.2484 79.3076 66.5351 80.2841C65.8218 81.2605 65.3391 82.386 65.1234 83.5759C64.9077 84.7657 64.9646 85.989 65.2897 87.1537C65.6148 88.3185 66.1997 89.3943 67.0004 90.3005M76.0005 95.0005V86.0005M76.0005 86.0005L80.0005 89.9991M76.0005 86.0005L72.0005 89.9991"
              stroke="white"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <defs>
              <filter
                id="filter0_dd_33_10927"
                x="0"
                y="16"
                width="152"
                height="104"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="4"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_33_10927"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="4" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_33_10927"
                />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="4"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect2_dropShadow_33_10927"
                />
                <feOffset dy="20" />
                <feGaussianBlur stdDeviation="12" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="effect1_dropShadow_33_10927"
                  result="effect2_dropShadow_33_10927"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect2_dropShadow_33_10927"
                  result="shape"
                />
              </filter>
              <linearGradient
                id="paint0_linear_33_10927"
                x1="25.9429"
                y1="37.4855"
                x2="71.2"
                y2="79.9998"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_33_10927"
                x1="53.0282"
                y1="26.8571"
                x2="109.6"
                y2="80"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_33_10927"
                x1="92.4002"
                y1="42.8002"
                x2="132"
                y2="80.0002"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
            </defs>
          </svg>

          <div class="upload-content">
            <h3 class="upload-title">Upload CV</h3>
            <p class="upload-subtitle">Charge an ongoing fee</p>

            <button @click="triggerFileInput" class="upload-button">
              Browse File
            </button>

            <p class="upload-info">or drag and drop</p>

            <p class="upload-formats">SVG, PNG, JPG or GIF (max. 800x400px)</p>
          </div>
        </template>
        <template v-else>
          <p v-if="errorMessage">{{ errorMessage }}</p>
          <div class="files_holder">
            <div
              v-for="(file, index) in files"
              :key="file.name"
              class="preview_item"
            >
              <div class="file_preview smaller">
                <img v-if="file.preview" :src="file.preview" :alt="file.name" />
                <img v-else-if="file.icon" :src="file.icon" alt="File icon" />
                <!-- <div>{{ file.name }}</div> -->
                <div
                  class="file_upload_progress"
                  v-if="props.showProgress && file.progress !== undefined"
                  :style="`width:${file.progress}%`"
                >
                  {{ file.progress }}%
                </div>

                <div class="preview_btn" @click="removeFile(index)">
                  <ff-icon icon="trash" />
                </div>
              </div>
            </div>
          </div>
          <div v-if="props.showProgress">
            Overall Progress: {{ overallProgress }}%
          </div>
        </template>
      </div>
      <template v-if="!uploadAutomatically">
        <button @click="uploadAllFilesToS3">Upload</button>
      </template>
    </div>
  </ClientOnly>
</template>
<script setup lang="ts">
import axios from "axios";

import { ref } from "vue";

interface Props {
  acceptTypes: string;
  maxFileSize: number; // in bytes
  maxFiles: number;
  showProgress: boolean;
  uploadAction: string;
  uploadAutomatically: boolean;
}

const props = defineProps<Props>();

const files = ref<
  (File & {
    preview?: string;
    icon?: string;
    progress?: number;
    cancel?: () => void;
    uploadParams?: { url: string; key?: string };
  })[]
>([]);
const dragOver = ref(false);
const errorMessage = ref<string>("");
const overallProgress = ref(0);
const fileInput = ref<HTMLInputElement>();

// Trigger file input click
function triggerFileInput() {
  fileInput.value?.click();
}

// Drag and Drop Handlers
function handleDrop(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;
  if (event.dataTransfer?.files) {
    handleFiles(event.dataTransfer.files);
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault();
  dragOver.value = true;
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;
}

// File Selection and Upload Handling
function handleFileSelection(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    handleFiles(input.files);
  }
}

async function handleFiles(fileList: FileList) {
  for (const file of Array.from(fileList)) {
    if (files.value.length >= props.maxFiles) break;
    if (fileIsValid(file)) {
      file.progress = 0;
      file.uploadParams = await handleUploadParams(file);
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        files.value.push({
          ...file,
          preview: reader.result as string,
        });

        if (props.uploadAutomatically) {
          uploadToS3(file);
        }
      };
      console.log(file);
    }
  }
}

// Fetch upload parameters
async function handleUploadParams(file) {
  try {
    const response = await get_upload_params({
      vehicle_id: "67c93f58-08ac-4acd-9fd1-c4805796eb4a",
      action: props.uploadAction,
      filename: file.name,
      content_type: file.type,
    });
    return response.data; // Assuming response.data contains { url, key }
  } catch (err) {
    console.error("Error fetching upload params:", err);
    errorMessage.value = "Failed to fetch upload parameters.";
    return null;
  }
}

// Validation, Preview Generation, and S3 Upload
function fileIsValid(file: File): boolean {
  const fileTypeIsValid = props.acceptTypes.split(",").includes(file.type);
  const fileSizeIsValid = file.size <= props.maxFileSize;
  if (!fileTypeIsValid || !fileSizeIsValid) {
    errorMessage.value = `Invalid file type or size: ${file.name}`;
    return false;
  }
  return true;
}

function uploadToS3(
  file: File & {
    progress?: number;
    cancel?: () => void;
    uploadParams?: { url: string; key?: string };
  }
) {
  if (!file.uploadParams?.url) {
    console.error("No upload URL provided for file:", file.name);
    return;
  }
  const config = {
    headers: {
      "Content-Type": file.type,
    },
    onUploadProgress: (progressEvent: any) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      file.progress = progress;
      updateOverallProgress();
    },
    cancelToken: new axios.CancelToken((cancel) => (file.cancel = cancel)),
  };

  axios
    .put(file.uploadParams.url, file, config)
    .then((response) => {
      console.log("File uploaded successfully");
      // files.value = files.value.filter((f) => f !== file); // Remove file after upload
      createFileServer(response);
      updateOverallProgress();
    })
    .catch((error) => {
      console.error("Error uploading file:", error);
    });
}
function uploadAllFilesToS3() {
  files.forEach((file) => {
    console.log("uploadAllFilesToS3");
    console.log(file);
    uploadToS3(file);
  });
}

function createFileServer(response) {
  console.log("createFileServer");
  delete response.config.data.preview;
  console.log(response.config.data);
  if (response.config.data) {
    const { name, size, type, uploadParams } = response.config.data;

    let apiData = {
      file_name: name,
      name: name,
      size: size,
      type: type,
      mime_type: type,
    };
    if (uploadParams) {
      if (uploadParams.required_fields.length > 0) {
        uploadParams.required_fields.forEach((field, index) => {
          if (field == "vehicle_id") {
            apiData[field] = "67c93f58-08ac-4acd-9fd1-c4805796eb4a";
          } else if (field == "action") {
            apiData[field] = props.uploadAction;
          }
        });
      }
    }
    if (apiData) {
      init_create_file(apiData);
    }
  }
}

async function init_create_file(fileData) {
  // Fetch upload parameters

  try {
    const response = await create_file(fileData);
    console.log(response);
    return response.data; // Assuming response.data contains { url, key }
  } catch (err) {
    console.error("Error fetching upload params:", err);
    errorMessage.value = "Failed to fetch upload parameters.";
    return null;
  }
}
// Progress and Removal Handlers
function updateOverallProgress() {
  const totalProgress = files.value.reduce(
    (total, file) => total + (file.progress || 0),
    0
  );
  overallProgress.value = files.value.length
    ? totalProgress / files.value.length
    : 0;
}

function removeFile(index: number) {
  const file = files.value[index];
  if (file.cancel) {
    file.cancel("Upload canceled by the user");
  }
  files.value.splice(index, 1);
  updateOverallProgress();
}

async function optimizeFileOnServer(file) {
  const formData = new FormData();
  formData.append("file", file); // Append the actual file

  try {
    const response = await axios.post("/api/optimize-image", formData, {
      responseType: "blob", // This tells Axios to expect a blob in the response
    });

    // Convert the Blob into a File object
    const optimizedFile = new File([response.data], file.name, {
      type: file.type,
    });

    console.log("RESPONSE");
    console.log(file);

    return optimizedFile;
  } catch (error) {
    console.error("Error optimizing file on server:", error);
    return null;
  }
}
</script>

<style scoped lang="scss">
.upload-container {
  .dropzone {
    width: 656px;
    height: 312px;
    left: 402px;
    top: 390px;
    background: #ffffff;
    box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.1),
      0px 0px 0px 1px rgba(18, 18, 18, 0.1);
    border-radius: 6px;
    overflow: hidden;

    // SVG positioning
    svg:first-of-type {
      position: relative;
      width: 404px;
      height: 404px;
      left: calc(50% - 404px / 2 + 6px);
      top: -51px;
    }

    svg:last-of-type {
      /* Illustration */
      position: relative;
      top: 0px;
      width: 152px;
      height: 118px;

      /* Inside auto layout */
    }

    .upload-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
    }

    .upload-title {
      font-size: 20px;
      font-weight: 600;
      color: #101828;
      margin: 0 0 4px 0;
      line-height: 1.2;
    }

    .upload-subtitle {
      font-size: 14px;
      color: #667085;
      margin: 0 0 32px 0;
      line-height: 1.4;
    }

    .upload-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: #ffffff;
      border: 1px solid #d0d5dd;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #344054;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 16px;

      &:hover {
        background: #f9fafb;
        border-color: #98a2b3;
      }

      &:focus {
        outline: none;
        box-shadow: 0px 0px 0px 4px #f4ebff,
          0px 1px 2px 0px rgba(16, 24, 40, 0.05);
        border-color: #7f56d9;
      }

      svg {
        width: 20px;
        height: 20px;
        color: #667085;
      }
    }

    .upload-info {
      font-size: 14px;
      color: #667085;
      margin: 0 0 16px 0;
      line-height: 1.4;
    }

    .upload-formats {
      font-size: 12px;
      color: #667085;
      margin: 0;
      line-height: 1.4;
    }

    &:hover,
    &.drag-over {
      background-color: #f9fafb;
      border-color: #7f56d9;
    }

    .files_holder {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      position: relative;
      .preview_item {
        .file_preview {
          display: flex;
          position: relative;
          min-width: 230px;
          max-width: 230px;
          flex-shrink: auto;
          background: #f1f1f1;
          border-radius: 12px;
          min-height: 180px;
          object-fit: cover;
          overflow: hidden;
          &.smaller {
            min-width: 140px;
            max-width: 140px;
            min-height: 120px;
          }
          .file_upload_progress {
            position: absolute;
            left: 0;
            height: 100%;
            display: flex;
            background: rgba(0, 0, 0, 0.2117647059);
            width: auto;
            transition: width 0.3s;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
          }
          img {
            border-radius: 12px;
          }
          .preview_btn {
            position: absolute;
            right: 10px;
            background: #fff;
            border-radius: 50px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-content: center;
            top: 10px;
          }
        }
      }
    }
  }

  input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
  }

  button {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 16px;
    cursor: pointer;

    &:hover {
      background-color: #0056b3;
    }
  }

  .preview {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100px;
      max-height: 100px;
      margin-right: 10px;
    }

    .progress {
      margin-left: 10px;
      color: #333;
    }
  }

  .progress-bar {
    width: 100%;
    background-color: #e0e0e0;
    margin-top: 10px;
    height: 20px;
    border-radius: 10px;

    .progress {
      height: 100%;
      background-color: #28a745;
      border-radius: 10px;
      text-align: center;
      line-height: 20px;
      color: white;
    }
  }

  p.error-message {
    color: red;
    font-size: 14px;
    margin-top: 10px;
  }
}
</style>
