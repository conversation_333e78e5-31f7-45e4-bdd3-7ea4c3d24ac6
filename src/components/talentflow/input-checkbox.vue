<template>
  <div class="checkbox-container">
    <input
      :id="inputId"
      v-model="internalValue"
      type="checkbox"
      :name="name"
      :disabled="disabled"
      :required="required"
      class="checkbox-input"
      @change="handleChange"
    />
    <label
      :for="inputId"
      class="checkbox-label"
      :class="{ checked: internalValue, disabled: disabled }"
    >
      <div class="checkbox-content">
        <div class="checkbox-left">
          <div v-if="leftIcon" class="checkbox-icon">
            <component
              v-if="iconMap[leftIcon]"
              :is="iconMap[leftIcon]"
              v-bind="defaultIconProps"
            />
          </div>
          <div class="checkbox-text">
            <h6 v-if="title" class="checkbox-title">{{ title }}</h6>
            <p v-if="description" class="checkbox-description">
              {{ description }}
            </p>
          </div>
        </div>
        <div class="checkbox-right">
          <div v-if="rightIcon" class="checkbox-icon">
            <component
              v-if="iconMap[rightIcon]"
              :is="iconMap[rightIcon]"
              v-bind="defaultIconProps"
            />
          </div>
        </div>
      </div>
    </label>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import {
  iconMap,
  type IconName,
  defaultIconProps,
} from "@/components/ff/iconMap";

interface Props {
  modelValue?: boolean;
  title: string;
  description?: string;
  name?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
  leftIcon?: IconName;
  rightIcon?: IconName;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  description: "",
  name: "",
  disabled: false,
  required: false,
  id: "",
});

const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  change: [value: boolean];
}>();

// Generate unique ID if not provided
const inputId = computed(() => {
  if (props.id) return props.id;
  return `checkbox-${Math.random().toString(36).substring(2, 11)}`;
});

// Internal value management
const internalValue = ref(props.modelValue);

// Watch for external changes
watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue;
  }
);

// Handle change event
const handleChange = () => {
  emit("update:modelValue", internalValue.value);
  emit("change", internalValue.value);
};

const onClick = (event: Event) => {
  if (props.isButton) {
    emit("click");
  } else if (props.to) {
    router.push(props.to);
  } else {
    if (!props.href) {
      event.preventDefault();
      emit("click");
    }
  }
};
</script>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.checkbox-container {
  position: relative;
  display: inline-block;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-label {
  display: block;
  width: 208px;
  height: 66px;
  background: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 14px !important;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;

  // Default state
  box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.1);

  &:hover {
    box-shadow: 0px 0px 0px 2px rgba(18, 18, 18, 0.05),
      0px 1px 1px rgba(18, 18, 18, 0.1), 0px 0px 0px 1px rgba(18, 18, 18, 0.15);
  }

  // Focused/checked state - exact styles as specified
  &.checked {
    box-shadow: 0px 0px 0px 4px rgba(18, 18, 18, 0.1),
      0px 1px 1px rgba(18, 18, 18, 0.1), 0px 0px 0px 1px rgba(18, 18, 18, 0.2),
      0px 1px 3px rgba(18, 18, 18, 0.1);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f9fafb;
  }
}

.checkbox-content {
  display: flex;
  align-items: start;
  justify-content: space-between;
  padding: 12px 16px;
  gap: 7px;
  height: 100%;
  box-sizing: border-box;
}

.checkbox-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-height: 0;
}

.checkbox-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.checkbox-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
  width: 24px;
  height: 24px;

  .checkbox-label.checked & {
    color: #121212;
  }

  .checkbox-label.disabled & {
    color: #9ca3af;
  }
}

.checkbox-text {
  flex: 1;
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0;
}

.checkbox-title {
  font-size: 14px;
  font-weight: 600;
  color: #121212;
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checkbox-description {
  font-size: 14px;
  color: #6b7280;
  margin: 2px 0 0 0;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checkbox-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  color: transparent;
  transition: all 0.2s ease;
  flex-shrink: 0;

  // Checked state - add the box inside
  .checkbox-label.checked & {
    border-color: #121212;
    color: white;

    // The inner box when checked
    &::before {
      content: "";
      position: absolute;
      width: 16px;
      height: 16px;
      left: calc(50% - 16px / 2);
      top: calc(50% - 16px / 2);
      background: #09090b;
      box-shadow: 0px 0px 0px 1px #121212;
      border-radius: 4px;
    }
  }

  .checkbox-label.disabled & {
    background: transparent;
    border-color: #9ca3af;
    color: transparent;

    &::before {
      background: #9ca3af;
      box-shadow: 0px 0px 0px 1px #9ca3af;
    }
  }

  // Hide the SVG checkmark since we're using the box
  svg {
    display: none;
  }
}

// Focus styles for accessibility
</style>
