<template>
  <ClientOnly>
    <div class="upload-container">
      <div
        class="dropzone"
        :class="{ 'drag-over': dragOver }"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
      >
        <input
          type="file"
          multiple
          @change="handleFileSelection"
          :accept="acceptTypes"
        />

        <template v-if="files.length == 0">
          <svg
            viewBox="0 0 64 64"
            xmlns="http://www.w3.org/2000/svg"
            style="
              display: block;
              height: 64px;
              width: 64px;
              fill: currentcolor;
            "
            aria-hidden="true"
            role="presentation"
            focusable="false"
          >
            <path
              d="M41.636 8.404l1.017 7.237 17.579 4.71a5 5 0 0 1 3.587 5.914l-.051.21-6.73 25.114A5.002 5.002 0 0 1 53 55.233V56a5 5 0 0 1-4.783 4.995L48 61H16a5 5 0 0 1-4.995-4.783L11 56V44.013l-1.69.239a5 5 0 0 1-5.612-4.042l-.034-.214L.045 14.25a5 5 0 0 1 4.041-5.612l.215-.035 31.688-4.454a5 5 0 0 1 5.647 4.256zm-20.49 39.373l-.14.131L13 55.914V56a3 3 0 0 0 2.824 2.995L16 59h21.42L25.149 47.812a3 3 0 0 0-4.004-.035zm16.501-9.903l-.139.136-9.417 9.778L40.387 59H48a3 3 0 0 0 2.995-2.824L51 56v-9.561l-9.3-8.556a3 3 0 0 0-4.053-.009zM53 34.614V53.19a3.003 3.003 0 0 0 2.054-1.944l.052-.174 2.475-9.235L53 34.614zM48 27H31.991c-.283.031-.571.032-.862 0H16a3 3 0 0 0-2.995 2.824L13 30v23.084l6.592-6.59a5 5 0 0 1 6.722-.318l.182.159.117.105 9.455-9.817a5 5 0 0 1 6.802-.374l.184.162L51 43.721V30a3 3 0 0 0-2.824-2.995L48 27zm-37 5.548l-5.363 7.118.007.052a3 3 0 0 0 3.388 2.553L11 41.994v-9.446zM25.18 15.954l-.05.169-2.38 8.876h5.336a4 4 0 1 1 6.955 0L48 25.001a5 5 0 0 1 4.995 4.783L53 30v.88l5.284 8.331 3.552-13.253a3 3 0 0 0-1.953-3.624l-.169-.05L28.804 14a3 3 0 0 0-3.623 1.953zM21 31a4 4 0 1 1 0 8 4 4 0 0 1 0-8zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM36.443 6.11l-.175.019-31.69 4.453a3 3 0 0 0-2.572 3.214l.02.175 3.217 22.894 5.833-7.74a5.002 5.002 0 0 1 4.707-4.12L16 25h4.68l2.519-9.395a5 5 0 0 1 5.913-3.587l.21.051 11.232 3.01-.898-6.397a3 3 0 0 0-3.213-2.573zm-6.811 16.395a2 2 0 0 0 1.64 2.496h.593a2 2 0 1 0-2.233-2.496zM10 13a4 4 0 1 1 0 8 4 4 0 0 1 0-8zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"
            ></path>
          </svg>
          <h5 class="mb-1">Drag & drop files here</h5>
          <p class="mb-1">or browse for photos</p>

          <a @click="uploadFiles" class="btn btn--primary mt-4">Browse</a>
          <p class="mt-2 smaller">
            Files allowed: Jpg, Png with max {{ maxFileSize }}MB
          </p>
        </template>
        <template v-else>
          <p v-if="errorMessage">{{ errorMessage }}</p>
          <div class="files_holder">
            <div
              v-for="(file, index) in files"
              :key="file.name"
              class="preview_item"
            >
              <div class="file_preview smaller">
                <img v-if="file.preview" :src="file.preview" :alt="file.name" />
                <img v-else-if="file.icon" :src="file.icon" alt="File icon" />
                <!-- <div>{{ file.name }}</div> -->
                <div
                  class="file_upload_progress"
                  v-if="props.showProgress && file.progress !== undefined"
                  :style="`width:${file.progress}%`"
                >
                  {{ file.progress }}%
                </div>

                <div class="preview_btn" @click="removeFile(index)">
                  <ff-icon icon="trash" />
                </div>
              </div>
            </div>
          </div>
          <div v-if="props.showProgress">
            Overall Progress: {{ overallProgress }}%
          </div>
        </template>
      </div>
      <template v-if="!uploadAutomatically">
        <button @click="uploadAllFilesToS3">Upload</button>
      </template>
    </div>
  </ClientOnly>
</template>
<script setup lang="ts">
import axios from "axios";
interface Props {
  acceptTypes: string;
  maxFileSize: number; // in bytes
  maxFiles: number;
  showProgress: boolean;
  uploadAction: string;
  uploadAutomatically: boolean;
}

const props = defineProps<Props>();
const { get_upload_params, create_file } = useAppApi();

const files = ref<
  (File & {
    preview?: string;
    icon?: string;
    progress?: number;
    cancel?: () => void;
    uploadParams?: { url: string; key?: string };
  })[]
>([]);
const dragOver = ref(false);
const errorMessage = ref<string>("");
const overallProgress = ref(0);

// Drag and Drop Handlers
function handleDrop(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;
  if (event.dataTransfer?.files) {
    handleFiles(event.dataTransfer.files);
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault();
  dragOver.value = true;
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;
}

// File Selection and Upload Handling
function handleFileSelection(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    handleFiles(input.files);
  }
}

async function handleFiles(fileList: FileList) {
  for (const file of Array.from(fileList)) {
    if (files.value.length >= props.maxFiles) break;
    if (fileIsValid(file)) {
      file.progress = 0;
      file.uploadParams = await handleUploadParams(file);
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        files.value.push({
          ...file,
          preview: reader.result as string,
        });

        if (props.uploadAutomatically) {
          uploadToS3(file);
        }
      };
      console.log(file);
    }
  }
}

// Fetch upload parameters
async function handleUploadParams(file) {
  try {
    const response = await get_upload_params({
      vehicle_id: "67c93f58-08ac-4acd-9fd1-c4805796eb4a",
      action: props.uploadAction,
      filename: file.name,
      content_type: file.type,
    });
    return response.data; // Assuming response.data contains { url, key }
  } catch (err) {
    console.error("Error fetching upload params:", err);
    errorMessage.value = "Failed to fetch upload parameters.";
    return null;
  }
}

// Validation, Preview Generation, and S3 Upload
function fileIsValid(file: File): boolean {
  const fileTypeIsValid = props.acceptTypes.split(",").includes(file.type);
  const fileSizeIsValid = file.size <= props.maxFileSize;
  if (!fileTypeIsValid || !fileSizeIsValid) {
    errorMessage.value = `Invalid file type or size: ${file.name}`;
    return false;
  }
  return true;
}

function uploadToS3(
  file: File & {
    progress?: number;
    cancel?: () => void;
    uploadParams?: { url: string; key?: string };
  }
) {
  if (!file.uploadParams?.url) {
    console.error("No upload URL provided for file:", file.name);
    return;
  }
  const config = {
    headers: {
      "Content-Type": file.type,
    },
    onUploadProgress: (progressEvent: any) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      file.progress = progress;
      updateOverallProgress();
    },
    cancelToken: new axios.CancelToken((cancel) => (file.cancel = cancel)),
  };

  axios
    .put(file.uploadParams.url, file, config)
    .then((response) => {
      console.log("File uploaded successfully");
      // files.value = files.value.filter((f) => f !== file); // Remove file after upload
      createFileServer(response);
      updateOverallProgress();
    })
    .catch((error) => {
      console.error("Error uploading file:", error);
    });
}
function uploadAllFilesToS3() {
  files.forEach((file) => {
    console.log("uploadAllFilesToS3");
    console.log(file);
    uploadToS3(file);
  });
}

function createFileServer(response) {
  console.log("createFileServer");
  delete response.config.data.preview;
  console.log(response.config.data);
  if (response.config.data) {
    const { name, size, type, uploadParams } = response.config.data;

    let apiData = {
      file_name: name,
      name: name,
      size: size,
      type: type,
      mime_type: type,
    };
    if (uploadParams) {
      if (uploadParams.required_fields.length > 0) {
        uploadParams.required_fields.forEach((field, index) => {
          if (field == "vehicle_id") {
            apiData[field] = "67c93f58-08ac-4acd-9fd1-c4805796eb4a";
          } else if (field == "action") {
            apiData[field] = props.uploadAction;
          }
        });
      }
    }
    if (apiData) {
      init_create_file(apiData);
    }
  }
}

async function init_create_file(fileData) {
  // Fetch upload parameters

  try {
    const response = await create_file(fileData);
    console.log(response);
    return response.data; // Assuming response.data contains { url, key }
  } catch (err) {
    console.error("Error fetching upload params:", err);
    errorMessage.value = "Failed to fetch upload parameters.";
    return null;
  }
}
// Progress and Removal Handlers
function updateOverallProgress() {
  const totalProgress = files.value.reduce(
    (total, file) => total + (file.progress || 0),
    0
  );
  overallProgress.value = files.value.length
    ? totalProgress / files.value.length
    : 0;
}

function removeFile(index: number) {
  const file = files.value[index];
  if (file.cancel) {
    file.cancel("Upload canceled by the user");
  }
  files.value.splice(index, 1);
  updateOverallProgress();
}

async function optimizeFileOnServer(file) {
  const formData = new FormData();
  formData.append("file", file); // Append the actual file

  try {
    const response = await axios.post("/api/optimize-image", formData, {
      responseType: "blob", // This tells Axios to expect a blob in the response
    });

    // Convert the Blob into a File object
    const optimizedFile = new File([response.data], file.name, {
      type: file.type,
    });

    console.log("RESPONSE");
    console.log(file);

    return optimizedFile;
  } catch (error) {
    console.error("Error optimizing file on server:", error);
    return null;
  }
}
</script>

<style scoped lang="scss">
.upload-container {
  .dropzone {
    position: relative;
    border: 2px dashed #b0b0b0;
    background-color: #fff;
    text-align: center;
    border-radius: 8px;
    padding: 32px;
    margin: 10px;
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center !important;
    align-items: center;
    svg {
      margin-bottom: 20px;
    }
    &:hover,
    &.drag-over {
      background-color: #e2eefd;
      border-color: #007bff;
    }

    .files_holder {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      position: relative;
      .preview_item {
        .file_preview {
          display: flex;
          position: relative;
          min-width: 230px;
          max-width: 230px;
          flex-shrink: auto;
          background: #f1f1f1;
          border-radius: 12px;
          min-height: 180px;
          object-fit: cover;
          overflow: hidden;
          &.smaller {
            min-width: 140px;
            max-width: 140px;
            min-height: 120px;
          }
          .file_upload_progress {
            position: absolute;
            left: 0;
            height: 100%;
            display: flex;
            background: rgba(0, 0, 0, 0.2117647059);
            width: auto;
            transition: width 0.3s;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
          }
          img {
            border-radius: 12px;
          }
          .preview_btn {
            position: absolute;
            right: 10px;
            background: #fff;
            border-radius: 50px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-content: center;
            top: 10px;
          }
        }
      }
    }
  }

  input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
  }

  button {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 16px;
    cursor: pointer;

    &:hover {
      background-color: #0056b3;
    }
  }

  .preview {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100px;
      max-height: 100px;
      margin-right: 10px;
    }

    .progress {
      margin-left: 10px;
      color: #333;
    }
  }

  .progress-bar {
    width: 100%;
    background-color: #e0e0e0;
    margin-top: 10px;
    height: 20px;
    border-radius: 10px;

    .progress {
      height: 100%;
      background-color: #28a745;
      border-radius: 10px;
      text-align: center;
      line-height: 20px;
      color: white;
    }
  }

  p.error-message {
    color: red;
    font-size: 14px;
    margin-top: 10px;
  }
}
</style>
