<template>
  <div>
    <input type="file" @change="handleFileUpload" multiple />
  </div>
</template>

<script setup lang="ts">
import axios from "axios";

interface FileUploadStatus {
  name: string;
  status: string;
}

const files = ref<FileUploadStatus[]>([]);

const getSign = async () => {
  try {
    // Fetch the presigned URL from your Nuxt server API
    const { data } = await axios.get("/api/presign", {
      params: { action: "xhani" },
    });
    console.log(data);
  } catch (error) {
    console.error("Error uploading file:", error);
  }
};
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.files) return;

  const uploadedFiles = Array.from(target.files);

  for (const file of uploadedFiles) {
    const fileType = file.type;
    const fileName = file.name;
    try {
      // Fetch the presigned URL from your Nuxt server API
      const { data } = await axios.get("/api/presign", {
        params: { filename: fileName, fileType: fileType },
      });

      // Proceed to upload the file using the presigned URL
      await uploadFileToS3(file, data.url);
      files.value.push({ name: fileName, status: "Uploaded successfully" });
    } catch (error) {
      console.error("Error uploading file:", error);
      files.value.push({ name: fileName, status: "Failed to upload" });
    }
  }
};

const uploadFileToS3 = async (file: File, url: string) => {
  const headers = {
    "Content-Type": file.type,
  };

  // Use the PUT method to upload the file to S3
  await axios.put(url, file, { headers });
};

onMounted(() => {
  getSign();
});
</script>
