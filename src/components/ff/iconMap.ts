import {
  CheckIcon,
  XIcon,
  EyeOffIcon,
  EyeIcon,
  X,
  Lock,
  LogIn,
  LogOut,
  HelpCircle,
  ArrowLeft,
  DollarSignIcon,
  MapPinIcon,
  BriefcaseIcon,
  SettingsIcon,
  Settings2Icon,
  PlusIcon,
  MinusIcon,
  ChevronDownIcon,
  PenLineIcon,
  ChevronUpIcon,
  LayoutGrid,
  SendIcon,
  MoonIcon,
  SunIcon,
  SquareCheckIcon,
} from "lucide-vue-next";
import GoogleIcon from "./icons/GoogleIcon.vue";
import LinkedinIcon from "./icons/LinkedinIcon.vue";
import ListIcon from "./icons/ListIcon.vue";
import Checkbox from "./icons/Checkbox.vue";

export interface IconProps {
  size?: number;
  strokeWidth?: number;
  color?: string;
  class?: string;
}

export const defaultIconProps: IconProps = {
  size: 20,
  strokeWidth: 2,
  class: "ff-icon",
};

export const iconMap = {
  check: CheckIcon,
  x: XIcon,
  eye: EyeIcon,
  eyeOff: EyeOffIcon,
  close: X,
  lock: Lock,
  login: LogIn,
  google: GoogleIcon,
  linkedin: LinkedinIcon,
  eyeClosed: EyeOffIcon,
  eyeOpen: EyeIcon,
  logout: LogOut,
  help: HelpCircle,
  arrowLeft: ArrowLeft,
  dollar: DollarSignIcon,
  mapPin: MapPinIcon,
  mapPin2: MapPinIcon,
  job: BriefcaseIcon,
  settings: SettingsIcon,
  settings2: Settings2Icon,
  plus: PlusIcon,
  minus: MinusIcon,
  chevronDown: ChevronDownIcon,
  chevronUp: ChevronUpIcon,
  penLine: PenLineIcon,
  layoutGrid: LayoutGrid,
  listIcon: ListIcon,
  sendIcon: SendIcon,
  moonIcon: MoonIcon,
  sunIcon: SunIcon,
  squareCheck: SquareCheckIcon,
  Checkbox: Checkbox,
  "": null,
} as const;

export type IconName = keyof typeof iconMap;
