<template>
  <div>
    <h1>
      upload
      <FF_uploader
        :acceptTypes="'image/*'"
        :maxFileSize="10485760"
        :maxFiles="1"
        :showProgress="true"
        :uploadAction="'/api/upload'"
        :uploadAutomatically="true"
      />
    </h1>
  </div>
</template>

<script setup lang="ts">
import FF_uploader from "@/components/talentflow/upload-v2.vue";
</script>

<style scoped></style>
